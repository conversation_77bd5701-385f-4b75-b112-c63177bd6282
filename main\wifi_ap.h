/*
 * WiFi AP Mode Configuration Header
 */

#pragma once

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize WiFi in AP mode with BLE coexistence optimizations
 *
 * This function initializes WiFi AP mode with the following optimizations:
 * - Open network (no password required) for easy access
 * - Reduced buffer sizes to minimize memory usage
 * - Power save mode for better BLE coexistence
 * - Channel 6 selection to reduce 2.4GHz interference
 * - 20MHz bandwidth to minimize spectrum usage
 * - Increased beacon interval to reduce air time
 * - Balanced coexistence preference
 * - Maximum 2 concurrent connections to reduce load
 *
 * @return esp_err_t ESP_OK on success
 */
esp_err_t wifi_init_ap(void);

/**
 * @brief Deinitialize and stop WiFi AP mode
 *
 * @return esp_err_t ESP_OK on success
 */
esp_err_t wifi_deinit_ap(void);

/**
 * @brief Simple WiFi AP initialization for testing
 *
 * This is a simplified version that uses minimal configuration
 * to test basic WiFi AP functionality.
 *
 * @return esp_err_t ESP_OK on success
 */
esp_err_t wifi_init_ap_simple(void);

#ifdef __cplusplus
}
#endif
